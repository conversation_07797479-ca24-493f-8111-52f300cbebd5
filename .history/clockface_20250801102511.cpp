#include "clockface.h"
#include "config.h"
#include "customsprite.h"
#include "mbedtls/base64.h"

#define IMAGE_BUFFER_SIZE 2048

// PNG decoder instance and buffer (static to avoid conflicts with image.cpp)
static PNG clockface_png;
static unsigned char imageBuffer[IMAGE_BUFFER_SIZE];
static size_t imageBufferLength;

// PNG position structure for callback
typedef struct png_position {
    uint8_t xoff, yoff;
} PNG_POSITION;

// PNG decoder callback function matching clockwise implementation
void PNGDraw(PNGDRAW *pDraw) {
    uint16_t usPixels[64];
    PNG_POSITION *pPos = (PNG_POSITION *)pDraw->pUser;
    
    clockface_png.getLineAsRGB565(pDraw, usPixels, PNG_RGB565_LITTLE_ENDIAN, 0xffffffff);
    
    if (pPos->yoff + pDraw->y < MATRIX_HEIGHT) {
        dma_display->drawRGBBitmap(pPos->xoff, pPos->yoff + pDraw->y, usPixels, pDraw->iWidth, 1);
    }
}

ClockfaceRenderer::ClockfaceRenderer(MatrixPanel_I2S_DMA *display) : doc(32768) {
    this->display = display;
    lastMillis = 0;
    lastSpriteUpdate = 0;
}

bool ClockfaceRenderer::isClockfaceFile(const String &filename) {
    return filename.endsWith(".json");
}

bool ClockfaceRenderer::loadClockface(const String &jsonContent) {
    DeserializationError error = deserializeJson(doc, jsonContent);
    if (error) {
        Serial.print("JSON parsing failed: ");
        Serial.println(error.c_str());
        return false;
    }
    
    Serial.printf("Loaded clockface: %s by %s\n", 
                  doc["name"].as<const char*>(), 
                  doc["author"].as<const char*>());
    
    setup();
    return true;
}

void ClockfaceRenderer::setup() {
    // Clear display with background color
    uint16_t bgColor = doc["bgColor"].as<uint16_t>();
    display->fillScreen(bgColor);
    
    // Render static elements
    renderElements(doc["setup"].as<JsonArrayConst>());
    
    // Initialize sprites
    createSprites();
    
    // Initial datetime refresh
    refreshDateTime();
}

void ClockfaceRenderer::update() {
    // Update date/time every second
    if (millis() - lastMillis >= 1000) {
        refreshDateTime();
        lastMillis = millis();
    }
    
    // Handle sprite animations
    handleSpriteAnimation();
}

void ClockfaceRenderer::setFont(const char *fontName) {
    if (fontName == nullptr || strlen(fontName) == 0) {
        display->setFont(); // Default font
        return;
    }
    
    if (strcmp(fontName, "picopixel") == 0) {
        display->setFont(&Picopixel);
    }
    else if (strcmp(fontName, "square") == 0) {
        display->setFont(&atariFont);
    }
    else if (strcmp(fontName, "big") == 0) {
        display->setFont(&hour8pt7b);
    }
    else if (strcmp(fontName, "medium") == 0) {
        display->setFont(&minute7pt7b);
    }
    else {
        display->setFont(); // Default font for unknown font names
    }
}

String ClockfaceRenderer::formatDateTime(const char* format) {
    if (!getLocalTime(&timeinfo)) {
        return "00:00";
    }
    
    char buffer[64];
    String formatStr = String(format);
    
    // Convert clockwise format to standard strftime format
    formatStr.replace("H", "%H");
    formatStr.replace("i", "%M");
    formatStr.replace("s", "%S");
    formatStr.replace("m", "%m");
    formatStr.replace("d", "%d");
    formatStr.replace("Y", "%Y");
    formatStr.replace("y", "%y");
    
    strftime(buffer, sizeof(buffer), formatStr.c_str(), &timeinfo);
    return String(buffer);
}

void ClockfaceRenderer::renderText(String text, JsonVariantConst value) {
    int16_t x1, y1;
    uint16_t w, h;
    
    setFont(value["font"].as<const char*>());
    
    // Get text bounds using the original clockwise method
    display->getTextBounds(text.c_str(), 0, 0, &x1, &y1, &w, &h);
    
    int x = value["x"].as<int>();
    int y = value["y"].as<int>();
    uint16_t fgColor = value["fgColor"].as<uint16_t>();
    uint16_t bgColor = value["bgColor"].as<uint16_t>();
    
    // BG Color - using the exact same calculation as clockwise
    display->fillRect(x + x1, y + y1, w, h, bgColor);
    
    // Set text color and draw text
    display->setTextColor(fgColor);
    display->setCursor(x, y);
    display->print(text);
}

void ClockfaceRenderer::refreshDateTime() {
    JsonArrayConst elements = doc["setup"].as<JsonArrayConst>();
    for (JsonVariantConst value : elements) {
        const char *type = value["type"].as<const char*>();
        
        if (strcmp(type, "datetime") == 0) {
            String formattedTime = formatDateTime(value["content"].as<const char*>());
            renderText(formattedTime, value);
        }
    }
}

void ClockfaceRenderer::renderElements(JsonArrayConst elements) {
    for (JsonVariantConst value : elements) {
        const char *type = value["type"].as<const char*>();
        
        if (strcmp(type, "text") == 0) {
            renderText(value["content"].as<const char*>(), value);
        }
        else if (strcmp(type, "fillrect") == 0) {
            display->fillRect(
                value["x"].as<int>(),
                value["y"].as<int>(),
                value["width"].as<int>(),
                value["height"].as<int>(),
                value["color"].as<uint16_t>());
        }
        else if (strcmp(type, "rect") == 0) {
            display->drawRect(
                value["x"].as<int>(),
                value["y"].as<int>(),
                value["width"].as<int>(),
                value["height"].as<int>(),
                value["color"].as<uint16_t>());
        }
        else if (strcmp(type, "line") == 0) {
            display->drawLine(
                value["x"].as<int>(),
                value["y"].as<int>(),
                value["x1"].as<int>(),
                value["y1"].as<int>(),
                value["color"].as<uint16_t>());
        }
        else if (strcmp(type, "image") == 0) {
            const char* imageData = value["image"].as<const char*>();
            if (imageData && strlen(imageData) > 0) {
                renderBase64PNG(imageData, value["x"].as<int>(), value["y"].as<int>());
            }
        }
    }
}

void ClockfaceRenderer::createSprites() {
    sprites.clear();
    
    JsonArrayConst loopElements = doc["loop"].as<JsonArrayConst>();
    for (size_t i = 0; i < loopElements.size(); i++) {
        JsonVariantConst value = loopElements[i];
        const char *type = value["type"].as<const char*>();
        
        if (strcmp(type, "sprite") == 0) {
            int spriteIndex = value["sprite"].as<int>();
            int x = value["x"].as<int>();
            int y = value["y"].as<int>();
            
            std::shared_ptr<CustomSprite> sprite = std::make_shared<CustomSprite>(x, y);
            
            // Get sprite dimensions from first frame like clockwise
            JsonArrayConst spriteFrames = doc["sprites"][spriteIndex].as<JsonArrayConst>();
            sprite->_totalFrames = spriteFrames.size();
            sprite->_spriteReference = i; // Reference to loop element index
            
            // Get actual dimensions from first frame PNG
            uint8_t width = 16, height = 16; // defaults
            if (spriteFrames.size() > 0) {
                const char* firstFrameImage = spriteFrames[0]["image"].as<const char*>();
                if (firstFrameImage && strlen(firstFrameImage) > 0) {
                    getImageDimensions(firstFrameImage, width, height);
                }
            }
            
            sprite->setDimensions(width, height);
            sprites.push_back(sprite);
        }
    }
}

void ClockfaceRenderer::handleSpriteAnimation() {
    if (sprites.empty()) {
        return;
    }
    
    JsonArrayConst loopElements = doc["loop"].as<JsonArrayConst>();
    
    for (auto& sprite : sprites) {
        JsonVariantConst loopElement = loopElements[sprite->_spriteReference];
        int spriteIndex = loopElement["sprite"].as<int>();
        
        // Get timing parameters
        uint32_t loopDelay = loopElement["loopDelay"].as<uint32_t>();
        if (loopDelay == 0) loopDelay = doc["delay"].as<uint32_t>();
        if (loopDelay == 0) loopDelay = 1000; // Default 1 second
        
        uint16_t frameDelay = loopElement["frameDelay"].as<uint16_t>();
        if (frameDelay == 0) frameDelay = doc["delay"].as<uint16_t>();
        if (frameDelay == 0) frameDelay = 250; // Default frame delay
        
        // Handle frame animation
        if (millis() - sprite->_lastMillisSpriteFrames >= frameDelay && sprite->_currentFrameCount < sprite->_totalFrames) {
            sprite->incFrame();
            
            // Handle sprite movement
            handleSpriteMovement(sprite);
            
            // Clear previous position (approximate)
            uint16_t bgColor = doc["bgColor"].as<uint16_t>();
            display->fillRect(sprite->getX(), sprite->getY(), sprite->getWidth(), sprite->getHeight(), bgColor);
            
            // Render current frame
            renderSprite(spriteIndex, sprite->_currentFrame, sprite->getX(), sprite->getY());
            
            sprite->_currentFrameCount++;
            sprite->_lastMillisSpriteFrames = millis();
        }
        
        // Handle loop reset timing
        if (millis() - sprite->_lastResetTime >= loopDelay) {
            unsigned long currentMillis = millis();
            unsigned long currentSecond = currentMillis / 1000; // Simple second calculation
            
            if ((currentSecond * 1000) % loopDelay == 0) {
                sprite->_currentFrameCount = 0;
                sprite->_currentFrame = 0;
                sprite->_lastResetTime = currentMillis;
            }
        }
    }
}

void ClockfaceRenderer::handleSpriteMovement(std::shared_ptr<CustomSprite> &sprite) {
    JsonArrayConst loopElements = doc["loop"].as<JsonArrayConst>();
    JsonVariantConst loopElement = loopElements[sprite->_spriteReference];
    
    // Get movement parameters from JSON
    unsigned long moveStartTime = loopElement["moveStartTime"].as<unsigned long>();
    if (moveStartTime == 0) moveStartTime = 1;
    
    unsigned long moveDuration = loopElement["moveDuration"].as<unsigned long>();
    int8_t moveInitialX = loopElement["x"].as<int8_t>();
    int8_t moveInitialY = loopElement["y"].as<int8_t>();
    int8_t moveTargetX = loopElement["moveTargetX"].as<int8_t>();
    int8_t moveTargetY = loopElement["moveTargetY"].as<int8_t>();
    bool shouldReturnToOrigin = loopElement["shouldReturnToOrigin"].as<bool>();
    
    // Handle active movement
    if (sprite->isMoving()) {
        unsigned long currentTime = millis();
        unsigned long elapsedTime = currentTime - sprite->_moveStartTime;
        float progress = (static_cast<float>(elapsedTime) / sprite->_moveDuration);
        
        int8_t oldX = sprite->getX();
        int8_t oldY = sprite->getY();
        int8_t newX = sprite->lerp(sprite->_moveInitialX, sprite->_moveTargetX, progress);
        int8_t newY = sprite->lerp(sprite->_moveInitialY, sprite->_moveTargetY, progress);
        
        // Calculate area to clear (union of old and new positions)
        int8_t clearX = min(oldX, newX);
        int8_t clearY = min(oldY, newY);
        int8_t clearWidth = sprite->getWidth() + max(oldX, newX) - clearX;
        int8_t clearHeight = sprite->getHeight() + max(oldY, newY) - clearY;
        
        // Clear the movement area
        uint16_t bgColor = doc["bgColor"].as<uint16_t>();
        display->fillRect(clearX, clearY, clearWidth, clearHeight, bgColor);
        
        if (progress <= 1.0f) {
            // Update sprite position
            sprite->setX(newX);
            sprite->setY(newY);
        } else if (sprite->shouldReturnToOrigin()) {
            // Movement complete, start return journey
            sprite->setX(sprite->_moveTargetX);
            sprite->setY(sprite->_moveTargetY);
            
            if (!sprite->_isReversing) {
                sprite->reverseMoving(moveInitialX, moveInitialY);
            }
        } else {
            // Movement complete, stop
            sprite->stopMoving();
        }
    }
    
    // Check if it's time to start movement
    if (moveDuration > 0 && (moveTargetX > -1 || moveTargetY > -1)) {
        if (millis() - sprite->_lastResetMoveTime >= moveStartTime) {
            unsigned long currentMillis = millis();
            unsigned long currentSecond = currentMillis / 1000;
            
            if ((currentSecond * 1000) % moveStartTime == 0) {
                sprite->_lastResetMoveTime = currentMillis;
                sprite->startMoving(moveTargetX, moveTargetY, moveDuration, shouldReturnToOrigin);
            }
        }
    }
}

void ClockfaceRenderer::renderSprite(int spriteIndex, int frameIndex, int x, int y) {
    JsonArrayConst spriteFrames = doc["sprites"][spriteIndex].as<JsonArrayConst>();
    if (frameIndex >= 0 && frameIndex < spriteFrames.size()) {
        JsonVariantConst frame = spriteFrames[frameIndex];
        const char* imageData = frame["image"].as<const char*>();
        
        if (imageData && strlen(imageData) > 0) {
            renderBase64PNG(imageData, x, y);
        }
    }
}

bool ClockfaceRenderer::renderBase64PNG(const char* base64Data, int x, int y) {
    if (!base64Data || strlen(base64Data) == 0) {
        return false;
    }
    
    // Decode base64 using mbedtls like the original clockwise
    int result = mbedtls_base64_decode(imageBuffer, IMAGE_BUFFER_SIZE, &imageBufferLength, 
                                      (const unsigned char *)base64Data, strlen(base64Data));
    
    if (result != 0) {
        Serial.println("Base64 decode failed");
        return false;
    }
    
    // Open PNG from RAM
    int rc = png.openRAM(imageBuffer, imageBufferLength, PNGDraw);
    
    if (rc == PNG_SUCCESS) {
        PNG_POSITION pos;
        pos.xoff = x;
        pos.yoff = y;
        
        // Decode the PNG with position info
        rc = png.decode((void *)&pos, 0);
        png.close();
    } else {
        Serial.printf("PNG open failed with code: %d\n", rc);
    }
    
    return (rc == PNG_SUCCESS);
}

void ClockfaceRenderer::getImageDimensions(const char* base64Data, uint8_t &width, uint8_t &height) {
    if (!base64Data || strlen(base64Data) == 0) {
        width = 16; // Default size
        height = 16;
        return;
    }
    
    // Decode base64 using mbedtls
    int result = mbedtls_base64_decode(imageBuffer, IMAGE_BUFFER_SIZE, &imageBufferLength, 
                                      (const unsigned char *)base64Data, strlen(base64Data));
    
    if (result != 0) {
        width = 16; // Default on failure
        height = 16;
        return;
    }
    
    // Open PNG to get dimensions
    int rc = png.openRAM(imageBuffer, imageBufferLength, PNGDraw);
    
    if (rc == PNG_SUCCESS) {
        width = png.getWidth();
        height = png.getHeight();
        png.close();
    } else {
        width = 16; // Default on failure
        height = 16;
    }
}