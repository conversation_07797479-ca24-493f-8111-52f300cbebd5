#ifndef CLOCKFACE_H
#define CLOCKFACE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <PNGdec.h>
#include <base64.h>
#include <SPIFFS.h>
#include "time.h"
#include "customsprite.h"

// Font includes
#include "picopixel.h"
#include "atari.h"
#include "hour8pt7b.h"
#include "minute7pt7b.h"

// Forward declarations
extern MatrixPanel_I2S_DMA *dma_display;

class ClockfaceRenderer {
private:
    DynamicJsonDocument doc;
    MatrixPanel_I2S_DMA *display;
    unsigned long lastMillis;
    unsigned long lastSpriteUpdate;
    struct tm timeinfo;
    
    // Sprite animation state
    std::vector<std::shared_ptr<CustomSprite>> sprites;
    
    // Helper methods
    void setFont(const char *fontName);
    void renderText(String text, JsonVariantConst value);
    void renderElements(JsonArrayConst elements);
    void refreshDateTime();
    void createSprites();
    void handleSpriteAnimation();
    void handleSpriteMovement(std::shared_ptr<CustomSprite> &sprite);
    void renderSprite(int spriteIndex, int frameIndex, int x, int y);
    bool renderBase64PNG(const char* base64Data, int x, int y);
    void getImageDimensions(const char* base64Data, uint8_t &width, uint8_t &height);
    String formatDateTime(const char* format);
    
public:
    ClockfaceRenderer(MatrixPanel_I2S_DMA *display);
    bool loadClockface(const String &jsonContent);
    void update();
    void setup();
    bool isClockfaceFile(const String &filename);
};

#endif