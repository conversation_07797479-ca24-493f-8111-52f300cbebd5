[{"Sourcefile": null, "Include": "", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32"}, {"Sourcefile": null, "Include": "", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "SPIFFS.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "FS.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "AnimatedGIF.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/AnimatedGIF/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "ESP32-HUB75-MatrixPanel-I2S-DMA.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "Adafruit_GFX.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "Adafruit_I2CDevice.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "Wire.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "SPI.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "WiFi.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "Network.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "AsyncTCP.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/Async_TCP/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "GFX_Layer.hpp", "Includepath": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "ESPAsyncWebServer.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "TJpg_Decoder.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/TJpg_Decoder/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "LittleFS.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/LittleFS/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "SD.h", "Includepath": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SD/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "PNGdec.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "ArduinoJson.h", "Includepath": "/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src"}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/esp32_divoom_clone_clockface.ino.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/clockface.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/config.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/customsprite.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/display.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/gif.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/image.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/webpages.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/build/sketch/webserver.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src/SPIFFS.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src/FS.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src/vfs_api.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/AnimatedGIF/src/AnimatedGIF.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-I2S-DMA.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-leddrivers.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32/esp32_i2s_parallel_dma.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32c6/dma_parallel_io.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32s3/gdma_lcd_parallel16.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GFX.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GrayOLED.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_SPITFT.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/glcdfont.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_BusIO_Register.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_GenericDevice.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_I2CDevice.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_SPIDevice.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src/SPI.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/AP.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/STA.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFi.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiAP.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiGeneric.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiMulti.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiSTA.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiScan.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkClient.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkEvents.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkInterface.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkManager.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkServer.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkUdp.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/Async_TCP/src/AsyncTCP.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/GFX_Layer.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/GFX_Lite.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/bitswap.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/colorpalettes.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/colorutils.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/glcdfont.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/hsv2rgb.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/lib8tion.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src/noise.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/AsyncEventSource.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/AsyncJson.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/AsyncMessagePack.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/AsyncWebHeader.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/AsyncWebSocket.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/BackPort_SHA1Builder.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/ChunkPrint.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/Middleware.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/WebAuthentication.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/WebHandlers.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/WebRequest.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/WebResponses.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src/WebServer.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/TJpg_Decoder/src/TJpg_Decoder.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/TJpg_Decoder/src/tjpgd.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/LittleFS/src/LittleFS.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SD/src/SD.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SD/src/sd_diskio.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SD/src/sd_diskio_crc.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/PNGdec.cpp", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/adler32.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/crc32.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/infback.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/inffast.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/inflate.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/inftrees.c", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/s3_simd_rgb565.S", "Include": "", "Includepath": null}, {"Sourcefile": "/home/<USER>/Documents/Arduino/libraries/PNGdec/src/zutil.c", "Include": "", "Includepath": null}]