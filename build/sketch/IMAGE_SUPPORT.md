#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/IMAGE_SUPPORT.md"
# Image Support for ESP32 Divoom Clone

## Overview
This ESP32 Divoom Clone now supports displaying static images in addition to animated GIFs. The system automatically scales images to fit the 64x64 RGB LED matrix display.

## Supported Image Formats
- **JPEG/JPG** - Full color images with compression
- **PNG** - Full color images with transparency support
- **BMP** - 24-bit uncompressed bitmap images
- **GIF** - Animated GIFs (existing functionality)

## Features
- **Automatic Scaling**: Images are automatically centered and scaled to fit the 64x64 display
- **Mixed Playback**: The system can play both GIFs and static images in sequence
- **Web Upload**: Upload images through the web interface
- **File Management**: View, play, and delete images through the web interface

## How It Works

### Image Processing
1. **JPEG**: Uses TJpg_Decoder library for efficient JPEG decoding
2. **PNG**: Uses PNGdec library for PNG decoding with transparency support
3. **BMP**: Custom BMP reader for 24-bit uncompressed files
4. **Scaling**: Images are automatically centered on the 64x64 display

### Display Logic
- Static images are displayed for 5 seconds by default (configurable)
- The system cycles through all media files (GIFs and images) in the filesystem
- GIFs play their full animation, then move to the next file
- Images display for the configured duration, then move to the next file

### Web Interface
- Upload button now accepts multiple file types: `.gif`, `.png`, `.jpg`, `.jpeg`, `.bmp`
- File list shows all media files with appropriate icons
- Play button works for both GIFs and images
- Preview functionality available for all supported formats

## Usage

### Uploading Images
1. Open the web interface in your browser
2. Click "Upload Media File" button
3. Select a GIF, PNG, JPG, JPEG, or BMP file
4. The file will be uploaded and automatically added to the playback rotation

### Playing Specific Images
1. In the file list, click the "Play" button next to any image
2. The image will be displayed immediately on the LED matrix
3. After the display duration, normal rotation will resume

## Technical Details

### Memory Usage
- Images are processed line-by-line to minimize memory usage
- Maximum supported image size for processing: 128x128 pixels
- Larger images are automatically scaled down

### Performance
- JPEG decoding is hardware-accelerated where possible
- PNG decoding supports various bit depths and color modes
- BMP reading is optimized for 24-bit files

### File Size Recommendations
- Keep image files under 100KB for best performance
- JPEG files are recommended for photographs
- PNG files are recommended for graphics with transparency
- BMP files should be used sparingly due to larger file sizes

## Configuration

### Display Duration
The image display duration can be modified in `image.cpp`:
```cpp
unsigned long imageDisplayDuration = 5000; // 5 seconds per image
```

### Supported Formats
To add support for additional formats, modify the helper functions in `image.cpp`:
```cpp
bool isImageFile(const String& fileName) {
  return isJpegFile(fileName) || isPngFile(fileName) || isBmpFile(fileName);
}
```

## Troubleshooting

### Images Not Displaying
1. Check that the file format is supported
2. Verify the file is not corrupted
3. Check available SPIFFS storage space
4. Monitor serial output for error messages

### Poor Image Quality
1. Try using JPEG format for photographs
2. Ensure source image has good contrast
3. Consider the 64x64 pixel limitation when creating images

### Upload Failures
1. Check file size (keep under 100KB)
2. Verify SPIFFS has available space
3. Check network connection stability
