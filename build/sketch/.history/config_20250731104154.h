#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/.history/config_20250731104154.h"
#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// Firmware and filesystem constants
#define FIRMWARE_VERSION "v0.4.5b"
#define FILESYSTEM SPIFFS

// ---------------------------
// 🔌 Pin Definitions (ESP32-S3 to HUB75 RGB Matrix)
// ---------------------------
#define R1_PIN 47
#define G1_PIN 1
#define B1_PIN 48
#define R2_PIN 45
#define G2_PIN 2
#define B2_PIN 0  // ⚠️ GPIO0 is BOOT pin, must stay HIGH at boot

#define A_PIN 35
#define B_PIN 41
#define C_PIN 36
#define D_PIN 40
#define E_PIN 42

#define LAT_PIN 39
#define OE_PIN 38
#define CLK_PIN 37

// ---------------------------
// 🧱 Panel Configuration
// ---------------------------
#define PANEL_RES_X 64
#define PANEL_RES_Y 64
#define PANEL_CHAIN 1

// Default configuration values
extern const String default_ssid;
extern const String default_wifipassword;
extern const String default_httpuser;
extern const String default_httppassword;
extern const int default_webserverporthttp;
extern const char* ntpServer;
extern const char* PARAM_INPUT;
extern const long gmtOffset_sec;
extern const int daylightOffset_sec;
extern const int maxGIFsPerPage;

// Configuration structure
struct Config {
  String ssid;
  String wifipassword;
  String httpuser;
  String httppassword;
  int webserverporthttp;
};

// Global configuration variables
extern Config config;
extern bool shouldReboot;

// Configuration functions
void initializeConfig();
void rebootESP(String message);

#endif