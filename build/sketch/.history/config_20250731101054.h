#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/.history/config_20250731101054.h"
#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// HTTP method definitions for ESP_Async_WebServer
// These must be defined before including ESPAsyncWebServer.h
#define HTTP_GET     1
#define HTTP_POST    2
#define HTTP_DELETE  4
#define HTTP_PUT     8
#define HTTP_PATCH   16
#define HTTP_HEAD    32
#define HTTP_OPTIONS 64
#define HTTP_ANY     127

// Firmware and filesystem constants
#define FIRMWARE_VERSION "v0.4.5b"
#define FILESYSTEM SPIFFS

// Hardware pin definitions
#define A_PIN   22 
#define B_PIN   32 
#define C_PIN   33
#define D_PIN   17 
#define E_PIN   21 

// Panel configuration
#define PANEL_RES_X 64      
#define PANEL_RES_Y 64     
#define PANEL_CHAIN 1      

// Default configuration values
extern const String default_ssid;
extern const String default_wifipassword;
extern const String default_httpuser;
extern const String default_httppassword;
extern const int default_webserverporthttp;
extern const char* ntpServer;
extern const char* PARAM_INPUT;
extern const long gmtOffset_sec;
extern const int daylightOffset_sec;
extern const int maxGIFsPerPage;

// Configuration structure
struct Config {
  String ssid;
  String wifipassword;
  String httpuser;
  String httppassword;
  int webserverporthttp;
};

// Global configuration variables
extern Config config;
extern bool shouldReboot;

// Configuration functions
void initializeConfig();
void rebootESP(String message);

#endif