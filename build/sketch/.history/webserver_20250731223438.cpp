#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/.history/webserver_20250731223438.cpp"
#include "webserver.h"
#include "display.h"
#include "gif.h"
#include <SPIFFS.h>

// Web server globals
AsyncWebServer *server;

void setupWiFi() {
  Serial.print("\nConnecting to Wifi: ");
  WiFi.begin(config.ssid.c_str(), config.wifipassword.c_str());
  (WiFi.status() != WL_CONNECTED); 

  Serial.println("\n\nNetwork Configuration:");
  Serial.println("----------------------");
  Serial.print("         SSID: "); Serial.println(WiFi.SSID());
  Serial.print("  Wifi Status: "); Serial.println(WiFi.status());
  Serial.print("Wifi Strength: "); Serial.print(WiFi.RSSI()); Serial.println(" dBm");
  Serial.print("          MAC: "); Serial.println(WiFi.macAddress());
  Serial.print("           IP: "); Serial.println(WiFi.localIP());
  Serial.print("       Subnet: "); Serial.println(WiFi.subnetMask());
  Serial.print("      Gateway: "); Serial.println(WiFi.gatewayIP());
  Serial.print("        DNS 1: "); Serial.println(WiFi.dnsIP(0));
  Serial.print("        DNS 2: "); Serial.println(WiFi.dnsIP(1));
  Serial.print("        DNS 3: "); Serial.println(WiFi.dnsIP(2));
  Serial.println();
}

void setupWebServer() {
  Serial.println("Configuring Webserver ...");
  server = new AsyncWebServer(config.webserverporthttp);
  configureWebServer();

  Serial.println("Starting Webserver ...");
  server->begin();
}

String listFiles(bool ishtml, int page, int pageSize) {
  String returnText = "";
  int fileIndex = 0;
  int startIndex = (page - 1) * pageSize;
  int endIndex = startIndex + pageSize;

  File root = SPIFFS.open("/");
  File foundfile = root.openNextFile();

  if (ishtml) {
    returnText += "<!DOCTYPE HTML><html lang=\"en\"><head>";
    returnText += "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">";
    returnText += "<meta charset=\"UTF-8\">";
    returnText += "</head><body>";
    returnText += "<table><tr><th>Name</th><th>Size</th><th>Preview</th><th>Actions</th></tr>";
  }

  while (foundfile) {
    if (fileIndex >= startIndex && fileIndex < endIndex) {
      if (ishtml) {
        returnText += "<tr><td>" + String(foundfile.name()) + "</td>";
        returnText += "<td>" + humanReadableSize(foundfile.size()) + "</td>";
        returnText += "<td><img src=\"/file?name=" + String(foundfile.name()) + "&action=show\" width=\"64\"></td>";
        returnText += "<td>";
        returnText += "<button onclick=\"downloadDeleteButton('" + String(foundfile.name()) + "', 'play')\">Play</button>";
        returnText += "<button onclick=\"downloadDeleteButton('" + String(foundfile.name()) + "', 'download')\">Download</button>";
        returnText += "<button onclick=\"downloadDeleteButton('" + String(foundfile.name()) + "', 'delete')\">Delete</button>";
        returnText += "</td></tr>";
      }
    }
    fileIndex++;
    foundfile = root.openNextFile();
  }

  if (ishtml) {
    returnText += "</table>";
    returnText += "<button onclick=\"window.location.href='/'\">Home</button>";
    if (page > 1) {
      returnText += "<button onclick=\"window.location.href='/list?page=" + String(page - 1) + "'\">Previous</button>";
    }
    if (fileIndex > endIndex) {
      returnText += "<button onclick=\"window.location.href='/list?page=" + String(page + 1) + "'\">Next</button>";
    }
    
    returnText += "<script>";
    returnText += "function downloadDeleteButton(filename, action) {";
    returnText += "    console.log(`downloadDeleteButton called with filename: ${filename}, action: ${action}`);";
    returnText += "    const url = `/file?name=${filename}&action=${action}`;";
    returnText += "    if (action === 'delete') {";
    returnText += "        fetch(url).then(response => response.text()).then(data => {";
    returnText += "            console.log(data);";
    returnText += "            alert('File deleted successfully!');";
    returnText += "            location.reload();";
    returnText += "        }).catch(error => {";
    returnText += "            console.error('Error deleting file:', error);";
    returnText += "            alert('Failed to delete file.');";
    returnText += "        });";
    returnText += "    } else if (action === 'download') {";
    returnText += "        window.open(url, '_blank');";
    returnText += "    } else if (action === 'play') {";
    returnText += "        fetch(url).then(response => response.text()).then(data => {";
    returnText += "            console.log(data);";
    returnText += "            alert('Playing file...');";
    returnText += "        }).catch(error => {";
    returnText += "            console.error('Error playing file:', error);";
    returnText += "        });";
    returnText += "    }";
    returnText += "}";
    returnText += "</script>";
    returnText += "</body></html>";
  }

  root.close();
  return returnText;
}

void configureWebServer() {
  server->onNotFound(notFound);
  server->onFileUpload(handleUpload);

  server->on("/logout", HTTP_GET, [](AsyncWebServerRequest * request) {
    request->requestAuthentication();
    request->send(401);
  });

  server->on("/logged-out", HTTP_GET, [](AsyncWebServerRequest * request) {
    String logmessage = "Client:" + request->client()->remoteIP().toString() + " " + request->url();
    Serial.println(logmessage);
    request->send(SPIFFS, "/logout.html", "text/html");
  });

  server->on("/", HTTP_GET, [](AsyncWebServerRequest * request) {
    String logmessage = "Client:" + request->client()->remoteIP().toString() + + " " + request->url();

    if (checkUserWebAuth(request)) {
      logmessage += " Auth: Success";
      Serial.println(logmessage);

      // Serve the static HTML file without template processing
      request->send(SPIFFS, "/index.html", "text/html");
    } else {
      logmessage += " Auth: Failed";
      Serial.println(logmessage);
      return request->requestAuthentication();
    }
  });

  // API endpoint for system info (JSON)
  server->on("/api/info", HTTP_GET, [](AsyncWebServerRequest * request) {
    if (checkUserWebAuth(request)) {
      String json = "{";
      json += "\"firmware\":\"" + String(FIRMWARE_VERSION) + "\",";
      json += "\"freeFlash\":\"" + humanReadableSize((SPIFFS.totalBytes() - SPIFFS.usedBytes())) + "\",";
      json += "\"usedFlash\":\"" + humanReadableSize(SPIFFS.usedBytes()) + "\",";
      json += "\"totalFlash\":\"" + humanReadableSize(SPIFFS.totalBytes()) + "\",";
      json += "\"sliderValue\":" + sliderValue + ",";
      json += "\"uptime\":" + String(millis()) + "";
      json += "}";

      request->send(200, "application/json", json);
    } else {
      request->send(401, "application/json", "{\"error\":\"Authentication required\"}");
    }
  });
  
  server->on("/slider", HTTP_GET, [] (AsyncWebServerRequest *request) {
    String inputMessage;
    if (request->hasParam(PARAM_INPUT)) {
      inputMessage = request->getParam(PARAM_INPUT)->value();
      sliderValue = inputMessage;
      dma_display->setBrightness8(sliderValue.toInt());
    } else {
      inputMessage = "No message sent";
    }
    Serial.print(inputMessage);
  });

  server->on("/reboot", HTTP_GET, [](AsyncWebServerRequest * request) {
    String logmessage = "Client:" + request->client()->remoteIP().toString() + " " + request->url();

    if (checkUserWebAuth(request)) {
      request->send(SPIFFS, "/reboot.html", "text/html");
      logmessage += " Auth: Success";
      Serial.println(logmessage);
      ESP.restart();
      shouldReboot = true;
    } else {
      logmessage += " Auth: Failed";
      Serial.println(logmessage);
      return request->requestAuthentication();
    }
  });

  server->on("/listfiles", HTTP_GET, [](AsyncWebServerRequest *request) {
    String logmessage = "Client:" + request->client()->remoteIP().toString() + " " + request->url();
    if (checkUserWebAuth(request)) {
      logmessage += " Auth: Success";
      Serial.println(logmessage);
      request->send(200, "text/plain", listFiles(true, 1, maxGIFsPerPage));
    } else {
      logmessage += " Auth: Failed";
      Serial.println(logmessage);
      return request->requestAuthentication();
    }
  });

  // API endpoint for file list (JSON)
  server->on("/api/files", HTTP_GET, [](AsyncWebServerRequest *request) {
    String logmessage = "Client:" + request->client()->remoteIP().toString() + " " + request->url();
    if (checkUserWebAuth(request)) {
      logmessage += " Auth: Success";
      Serial.println(logmessage);

      int page = 1;
      if (request->hasParam("page")) {
        page = request->getParam("page")->value().toInt();
      }

      int itemsPerPage = 10;
      if (request->hasParam("limit")) {
        itemsPerPage = request->getParam("limit")->value().toInt();
      }

      String json = listFilesJSON(page, itemsPerPage);
      request->send(200, "application/json", json);
    } else {
      logmessage += " Auth: Failed";
      Serial.println(logmessage);
      return request->requestAuthentication();
    }
  });

  server->on("/list", HTTP_GET, [](AsyncWebServerRequest *request) {
    int page = 1;
    if (request->hasParam("page")) {
      page = request->getParam("page")->value().toInt();
    }
    String fileList = listFiles(true, page, maxGIFsPerPage);
    request->send(200, "text/html", fileList);
  });

  server->on("/setColor", HTTP_GET, [](AsyncWebServerRequest *request) {
    if (request->hasParam("r") && request->hasParam("g") && request->hasParam("b")) {
      colorR = request->getParam("r")->value().toInt();
      colorG = request->getParam("g")->value().toInt();
      colorB = request->getParam("b")->value().toInt();
      Serial.printf("Color updated: R=%d, G=%d, B=%d\n", colorR, colorG, colorB);
      request->send(200, "text/plain", "Color updated");
    } else {
      request->send(400, "text/plain", "Missing parameters");
    }
  });

  server->on("/toggleGIF", HTTP_GET, [](AsyncWebServerRequest *request) {
    if (request->hasParam("state")) {
      String state = request->getParam("state")->value();
      gifEnabled = (state == "on");
      Serial.printf("GIF playback state changed: %s\n", gifEnabled ? "ON" : "OFF");
      request->send(200, "text/plain", "GIF playback state updated");
    } else {
      request->send(400, "text/plain", "Missing 'state' parameter");
    }
  });
 
  server->on("/toggleLoopGif", HTTP_GET, [](AsyncWebServerRequest *request) {
    if (request->hasParam("state")) {
      String state = request->getParam("state")->value();
      loopGifEnabled = (state == "on");
      Serial.printf("Loop GIF state updated to: %s\n", loopGifEnabled ? "ON" : "OFF");
      request->send(200, "text/plain", "Loop GIF state updated");
    } else {
      request->send(400, "text/plain", "Missing 'state' parameter");
    }
  });

  server->on("/toggleClock", HTTP_GET, [](AsyncWebServerRequest *request) {
    if (request->hasParam("state")) {
      String state = request->getParam("state")->value();
      clockEnabled = (state == "on");
      if (clockEnabled) {
        scrollTextEnabled = false;
      }
      Serial.printf("Clock state changed: %s\n", clockEnabled ? "ON" : "OFF");
      request->send(200, "text/plain", "Clock state updated");
    } else {
      request->send(400, "text/plain", "Missing 'state' parameter");
    }
  });

  server->on("/toggleScrollText", HTTP_GET, [](AsyncWebServerRequest *request) {
    if (request->hasParam("state")) {
      String state = request->getParam("state")->value();
      scrollTextEnabled = (state == "on");
      if (scrollTextEnabled) {
        clockEnabled = false;
      }
      Serial.printf("Scrolling text state changed: %s\n", scrollTextEnabled ? "ON" : "OFF");
      request->send(200, "text/plain", "Scrolling text state updated");
    } else {
      request->send(400, "text/plain", "Missing 'state' parameter");
    }
  });

  server->on("/updateScrollText", HTTP_GET, [](AsyncWebServerRequest *request) {
    if (request->hasParam("text") && request->hasParam("fontSize") && request->hasParam("speed")) {
      scrollText = request->getParam("text")->value();
      scrollFontSize = request->getParam("fontSize")->value().toInt();
      scrollSpeed = request->getParam("speed")->value().toInt();
      Serial.printf("Scrolling text updated: '%s', Font size: %d, Speed: %d\n", scrollText.c_str(), scrollFontSize, scrollSpeed);

      // Force display the scrolling text immediately for testing
      ForceDisplayScrollText();

      request->send(200, "text/plain", "Scrolling text updated");
    } else {
      request->send(400, "text/plain", "Missing parameters");
    }
  });

  server->on("/file", HTTP_GET, [](AsyncWebServerRequest * request) {
    String logmessage = "Client:" + request->client()->remoteIP().toString() + " " + request->url();
    if (checkUserWebAuth(request)) {
      logmessage += " Auth: Success";
      Serial.println(logmessage);

      if (request->hasParam("name") && request->hasParam("action")) {
        String fileName = "/"+String(request->getParam("name")->value() );
        const char *fileAction = request->getParam("action")->value().c_str();

        logmessage = "Client:" + request->client()->remoteIP().toString() + " " + request->url() + "?name=" + String(fileName) + "&action=" + String(fileAction);

        if (!SPIFFS.exists(fileName)) {
          Serial.println(logmessage + " ERROR: file does not exist");
          request->send(400, "text/plain", "ERROR: file does not exist");
        } else {
          Serial.println(logmessage + " file exists");
         
          if (strcmp(fileAction, "download") == 0) {
            logmessage += " downloaded";
            request->send(SPIFFS, fileName, "application/octet-stream");
          } else if (strcmp(fileAction, "delete") == 0) {
            logmessage += " deleted";
            SPIFFS.remove(fileName);
            request->send(200, "text/plain", "Deleted File: " + String(fileName));
          } else if (strcmp(fileAction, "play") == 0) {
            requestedGifPath = fileName;  // This now handles both GIFs and images
            gifFile = SPIFFS.open(fileName);
            logmessage += " opening";
          } else if (strcmp(fileAction, "show") == 0) {
            logmessage += " previewing"; 
            delay(100);
            request->send(SPIFFS, fileName, "image/gif");
          } else {
            logmessage += " ERROR: invalid action param supplied";
            request->send(400, "text/plain", "ERROR: invalid action param supplied");
          }
          Serial.println(logmessage);
        }
      } else {
        request->send(400, "text/plain", "ERROR: name and action params required");
      }
    } else {
      logmessage += " Auth: Failed";
      Serial.println(logmessage);
      return request->requestAuthentication();
    }
  });
}

void notFound(AsyncWebServerRequest *request) {
  String logmessage = "Client:" + request->client()->remoteIP().toString() + " " + request->url();
  Serial.println(logmessage);
  request->send(404, "text/plain", "Not found");
}

bool checkUserWebAuth(AsyncWebServerRequest * request) {
  bool isAuthenticated = false;

  if (request->authenticate(config.httpuser.c_str(), config.httppassword.c_str())) {
    Serial.println("is authenticated via username and password");
    isAuthenticated = true;
  }
  return isAuthenticated;
}

void handleUpload(AsyncWebServerRequest *request, String filename, size_t index, uint8_t *data, size_t len, bool final) {
  if (checkUserWebAuth(request)) {
    String logmessage = "Client:" + request->client()->remoteIP().toString() + " " + request->url();
    Serial.println(logmessage);

    if (!index) {
      logmessage = "Upload Start: " + String(filename);
      request->_tempFile = SPIFFS.open("/" + filename, "w");
      Serial.println(logmessage);
    }

    if (len) {
      request->_tempFile.write(data, len);
      logmessage = "Writing file: " + String(filename) + " index=" + String(index) + " len=" + String(len);
      Serial.println(logmessage);
    }

    if (final) {
      logmessage = "Upload Complete: " + String(filename) + ",size: " + String(index + len);
      request->_tempFile.close();
      Serial.println(logmessage);
      // Send success response instead of redirect for AJAX uploads
      request->send(200, "text/plain", "Upload successful");
    }
  } else {
    Serial.println("Auth: Failed");
    return request->requestAuthentication();
  }
}

// Generate a JSON response with file list
String listFilesJSON(int page, int itemsPerPage) {
  String json = "{\"files\":[";
  File root = SPIFFS.open("/");
  File foundfile = root.openNextFile();

  int fileCount = 0;
  int totalFiles = 0;
  int startIndex = (page - 1) * itemsPerPage;
  int endIndex = startIndex + itemsPerPage;
  bool firstFile = true;

  // Count total files first
  while (foundfile) {
    if (foundfile.name()[0] != '.') {
      totalFiles++;
    }
    foundfile = root.openNextFile();
  }

  // Reset to beginning
  root.close();
  root = SPIFFS.open("/");
  foundfile = root.openNextFile();

  // Build JSON for requested page
  while (foundfile) {
    if (foundfile.name()[0] != '.') {  // Skip hidden files
      if (fileCount >= startIndex && fileCount < endIndex) {
        if (!firstFile) json += ",";
        json += "{";
        json += "\"name\":\"" + String(foundfile.name()) + "\",";
        json += "\"size\":\"" + humanReadableSize(foundfile.size()) + "\",";
        json += "\"sizeBytes\":" + String(foundfile.size()) + ",";
        json += "\"type\":\"" + String(foundfile.name()).substring(String(foundfile.name()).lastIndexOf('.') + 1) + "\"";
        json += "}";
        firstFile = false;
      }
      fileCount++;
    }
    foundfile = root.openNextFile();
  }

  json += "],";
  json += "\"pagination\":{";
  json += "\"currentPage\":" + String(page) + ",";
  json += "\"totalFiles\":" + String(totalFiles) + ",";
  json += "\"itemsPerPage\":" + String(itemsPerPage) + ",";
  json += "\"totalPages\":" + String((totalFiles + itemsPerPage - 1) / itemsPerPage);
  json += "}}";

  return json;
}