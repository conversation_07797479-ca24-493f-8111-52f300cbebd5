#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/.history/display_20250731104303.cpp"
#include "display.h"
#include <SPIFFS.h>
#include <WiFi.h>

// Display globals
MatrixPanel_I2S_DMA *dma_display = nullptr;

// Color variables
uint16_t myBLACK, myWHITE, myRED, myGREEN, myBLUE;
uint8_t colorR = 255;
uint8_t colorG = 255;
uint8_t colorB = 255;
uint8_t scrollFontSize = 2;
uint8_t scrollSpeed = 18;
int16_t xOne, yOne;
uint16_t w, h;

// Text and animation variables
int textXPosition = 64;
int textYPosition = 24;
unsigned long lastPixelToggle = 0;
unsigned long lastScrollUpdate = 0;
unsigned long isAnimationDue;
bool showFirstSet = true;
bool clockEnabled = true;
bool gifEnabled = true;
bool scrollTextEnabled = false;
bool loopGifEnabled = true;

// Text and slider variables
String inputMessage;
String sliderValue = "100";
String scrollText = "Hello";

// Layer objects
void layer_draw_callback(int16_t x, int16_t y, uint8_t r_data, uint8_t g_data, uint8_t b_data) {
  dma_display->drawPixel(x, y, dma_display->color565(r_data, g_data, b_data));
}

GFX_Layer gfx_layer_bg(64, 64, layer_draw_callback);
GFX_Layer gfx_layer_fg(64, 64, layer_draw_callback);
GFX_LayerCompositor gfx_compositor(layer_draw_callback);

void setupDisplay() {
  HUB75_I2S_CFG mxconfig(
    PANEL_RES_X,
    PANEL_RES_Y,
    PANEL_CHAIN
  );

  mxconfig.gpio.r1 = R1_PIN;
  mxconfig.gpio.g1 = G1_PIN;
  mxconfig.gpio.b1 = B1_PIN;
  mxconfig.gpio.r2 = R2_PIN;
  mxconfig.gpio.g2 = G2_PIN;
  mxconfig.gpio.b2 = B2_PIN;

  mxconfig.gpio.a = A_PIN;
  mxconfig.gpio.b = B_PIN;
  mxconfig.gpio.c = C_PIN;
  mxconfig.gpio.d = D_PIN;
  mxconfig.gpio.e = E_PIN;

  mxconfig.gpio.lat = LAT_PIN;
  mxconfig.gpio.oe = OE_PIN;
  mxconfig.gpio.clk = CLK_PIN;

  mxconfig.clkphase = false;
  mxconfig.driver = HUB75_I2S_CFG::FM6124;

  dma_display = new MatrixPanel_I2S_DMA(mxconfig);
  dma_display->setRotation(0);
  dma_display->begin();
  dma_display->setBrightness8(sliderValue.toInt());
  dma_display->clearScreen();

  // Initialize colors
  myBLACK = dma_display->color565(0, 0, 0);
  myWHITE = dma_display->color565(255, 255, 255);
  myRED = dma_display->color565(255, 0, 0);
  myGREEN = dma_display->color565(0, 255, 0);
  myBLUE = dma_display->color565(0, 0, 255);
}

String humanReadableSize(const size_t bytes) {
  if (bytes < 1024) return String(bytes) + " B";
  else if (bytes < (1024 * 1024)) return String(bytes / 1024.0) + " KB";
  else if (bytes < (1024 * 1024 * 1024)) return String(bytes / 1024.0 / 1024.0) + " MB";
  else return String(bytes / 1024.0 / 1024.0 / 1024.0) + " GB";
}

void showStartupInfo() {
  dma_display->fillScreen(dma_display->color565(0, 0, 0));
  dma_display->setTextSize(1);
  delay(1000);
  dma_display->print("ID:"); dma_display->print(FIRMWARE_VERSION);  
  dma_display->setCursor(0, 16);
  dma_display->print("IP:"); dma_display->print(WiFi.localIP());
  dma_display->setCursor(0, 38);  
  dma_display->print("RSSI:"); dma_display->println(WiFi.RSSI());
  dma_display->setCursor(0, 52);  
  dma_display->print("SSID:"); dma_display->println(WiFi.SSID());
  delay(1000);
  dma_display->fillScreen(dma_display->color565(0, 0, 0));
  gfx_layer_fg.clear();
  gfx_layer_bg.clear();
}