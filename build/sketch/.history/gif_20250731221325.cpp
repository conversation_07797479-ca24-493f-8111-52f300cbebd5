#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/.history/gif_20250731221325.cpp"
#include "gif.h"
#include "time.h"
#include <SPIFFS.h>

// GIF globals
AnimatedGIF gif;
File f;
int x_offset, y_offset;
String currentGifPath = "";
String requestedGifPath = "";
String gifDir = "/";
char filePath[256] = { 0 };
File root, gifFile;
unsigned long start_tick = 0;

// Helper function to check if a file is a GIF
bool isGifFile(const String& fileName) {
  return fileName.endsWith(".gif") || fileName.endsWith(".GIF");
}

// Helper function to check if a file is a media file (GIF or Image)
bool isMediaFile(const String& fileName) {
  return isGifFile(fileName) ||
         fileName.endsWith(".png") || fileName.endsWith(".PNG") ||
         fileName.endsWith(".jpg") || fileName.endsWith(".JPG") ||
         fileName.endsWith(".jpeg") || fileName.endsWith(".JPEG") ||
         fileName.endsWith(".bmp") || fileName.endsWith(".BMP");
}

void GIFDraw(GIFDRAW *pDraw) {
  uint8_t *s;
  uint16_t *d, *usPalette, usTemp[320];
  int x, y, iWidth;

  iWidth = pDraw->iWidth;
  if (iWidth > MATRIX_WIDTH)
    iWidth = MATRIX_WIDTH;

  usPalette = pDraw->pPalette;
  y = pDraw->iY + pDraw->y;
  
  s = pDraw->pPixels;
  if (pDraw->ucDisposalMethod == 2) {
    for (x=0; x<iWidth; x++) {
      if (s[x] == pDraw->ucTransparent)
        s[x] = pDraw->ucBackground;
    }
    pDraw->ucHasTransparency = 0;
  }

  if (gifEnabled) {
    if (pDraw->ucHasTransparency) {
      uint8_t *pEnd, c, ucTransparent = pDraw->ucTransparent;
      int x, iCount;
      pEnd = s + pDraw->iWidth;
      x = 0;
      iCount = 0;
      while(x < pDraw->iWidth) {
        c = ucTransparent-1;
        d = usTemp;
        while (c != ucTransparent && s < pEnd) {
          c = *s++;
          if (c == ucTransparent) {
            s--;
          } else {
            *d++ = usPalette[c];
            iCount++;
          }
        }
        if (iCount) {
          for(int xOffset = 0; xOffset < iCount; xOffset++ ){
            gfx_layer_bg.drawPixel(x + xOffset, y, usTemp[xOffset]);
          }
          x += iCount;
          iCount = 0;
        }
        c = ucTransparent;
        while (c == ucTransparent && s < pEnd) {
          c = *s++;
          if (c == ucTransparent)
            iCount++;
          else
            s--; 
        }
        if (iCount) {
          x += iCount;
          iCount = 0;
        }
      }
    } else {
      s = pDraw->pPixels;
      for (x=0; x<pDraw->iWidth; x++) {
        gfx_layer_bg.drawPixel(x, y, usPalette[*s++]);
      }
    }
  } else {     
    gfx_layer_bg.clear();
  }
    
  // Call the separate display layers update function
  UpdateDisplayLayers();
}

// Separate function to update display layers (scrolling text, clock, etc.)
// This should be called regularly, not just during GIF playback
void UpdateDisplayLayers() {
  // Clock display logic
  if (clockEnabled) {
    struct tm timeinfo;
    if(!getLocalTime(&timeinfo)){
      Serial.println("Failed to obtain time");
    }
    gfx_layer_fg.clear();
    gfx_layer_fg.setTextColor(gfx_layer_fg.color565(colorR, colorG, colorB));
    gfx_layer_fg.setTextSize(2);
    gfx_layer_fg.setCursor(7, 24);
    gfx_layer_fg.print(&timeinfo, "%I");
    gfx_layer_fg.setCursor(35, 24);
    gfx_layer_fg.print(&timeinfo, "%M");

    if (millis() - lastPixelToggle >= 1000) {
      showFirstSet = !showFirstSet;
      lastPixelToggle = millis();
    }
    if (showFirstSet) {
      gfx_layer_fg.setTextColor(gfx_layer_fg.color565(colorR, colorG, colorB));
      gfx_layer_fg.setTextSize(1);
      gfx_layer_fg.setCursor(28, 28);
      gfx_layer_fg.print(".");
      gfx_layer_fg.setCursor(28, 22);
      gfx_layer_fg.print(".");
    } else {
      gfx_layer_fg.setTextColor(gfx_layer_fg.color565(0, 0, 0));
      gfx_layer_fg.setTextSize(1);
      gfx_layer_fg.setCursor(28, 28);
      gfx_layer_fg.print(".");
      gfx_layer_fg.setCursor(28, 22);
      gfx_layer_fg.print(".");
    }
  }

  // Scrolling text logic
  if (scrollTextEnabled) {
    gfx_layer_fg.setTextWrap(false);

    if (scrollFontSize == 1) {
      textYPosition = 27;
    } else if (scrollFontSize == 2) {
      textYPosition = 24;
    } else if (scrollFontSize == 3) {
      textYPosition = 20;
    } else if (scrollFontSize == 4) {
      textYPosition = 16;
    } else {
      textYPosition = 24;
    }

    byte offSet = 25;
    unsigned long now = millis();
    if (now > isAnimationDue) {
      gfx_layer_fg.setTextSize(scrollFontSize);
      isAnimationDue = now + scrollSpeed;
      textXPosition -= 1;

      gfx_layer_fg.getTextBounds(scrollText.c_str(), textXPosition, textYPosition, &xOne, &yOne, &w, &h);
      if (textXPosition + w <= 0) {
        textXPosition = gfx_layer_fg.width() + offSet;
      }

      gfx_layer_fg.setCursor(textXPosition, textYPosition);
      gfx_layer_fg.drawRect(0, textYPosition - 12, gfx_layer_fg.width(), 42, gfx_layer_fg.color565(0, 0, 0));
      gfx_layer_fg.fillRect(0, textYPosition - 12, gfx_layer_fg.width(), 42, gfx_layer_fg.color565(0, 0, 0));

      uint8_t w = 0;
      for (w = 0; w < strlen(scrollText.c_str()); w++) {
        gfx_layer_fg.setTextColor(gfx_layer_fg.color565(colorR, colorG, colorB));
        gfx_layer_fg.print(scrollText.c_str()[w]);
      }
    }
  }

  if (!scrollTextEnabled && !clockEnabled) {
    gfx_layer_fg.clear();
  }

  // Blend the layers
  gfx_layer_bg.dim(150);
  gfx_layer_fg.dim(255);
  gfx_compositor.Blend(gfx_layer_bg, gfx_layer_fg);
}

void * GIFOpenFile(const char *fname, int32_t *pSize) {
  Serial.print("Playing gif: ");
  Serial.println(fname);

  // Check if file has .gif extension
  String fileName = String(fname);
  if (!isGifFile(fileName)) {
    Serial.println("ERROR: File is not a GIF file, skipping");
    return NULL;
  }

  f = SPIFFS.open(fname);
  if (f) {
    *pSize = f.size();
    return (void *)&f;
  } else {
    Serial.println("ERROR: Could not open file");
  }
  return NULL;
}

void GIFCloseFile(void *pHandle) {
  File *f = static_cast<File *>(pHandle);
  if (f != NULL)
    f->close();
}

int32_t GIFReadFile(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen) {
  int32_t iBytesRead;
  iBytesRead = iLen;
  File *f = static_cast<File *>(pFile->fHandle);
  if ((pFile->iSize - pFile->iPos) < iLen)
    iBytesRead = pFile->iSize - pFile->iPos - 1;
  if (iBytesRead <= 0)
    return 0;
  iBytesRead = (int32_t)f->read(pBuf, iBytesRead);
  pFile->iPos = f->position();
  return iBytesRead;
}

int32_t GIFSeekFile(GIFFILE *pFile, int32_t iPosition) { 
  int i = micros();
  File *f = static_cast<File *>(pFile->fHandle);
  f->seek(iPosition);
  pFile->iPos = (int32_t)f->position();
  i = micros() - i;
  return pFile->iPos;
}

void ShowGIF(char *name) {
  start_tick = millis();
  unsigned long lastTimeCheck = millis();
  if (gif.open(name, GIFOpenFile, GIFCloseFile, GIFReadFile, GIFSeekFile, GIFDraw)) {
    x_offset = (MATRIX_WIDTH - gif.getCanvasWidth())/2;
    if (x_offset < 0) x_offset = 0;
    y_offset = (MATRIX_HEIGHT - gif.getCanvasHeight())/2;
    if (y_offset < 0) y_offset = 0;
    Serial.printf("Successfully opened GIF; Canvas size = %d x %d\n", gif.getCanvasWidth(), gif.getCanvasHeight());
    Serial.flush();
    while (gif.playFrame(true, NULL)) {
      // Display layer blending is now handled in UpdateDisplayLayers()
      // which is called from GIFDraw()

      if ( (millis() - start_tick) > 50000) {
        // Will change to the next gif in the list after the set time.
      }
    }
    gif.close();
  }
}