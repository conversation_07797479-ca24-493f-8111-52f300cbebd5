#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/customsprite.h"
#ifndef CUSTOMSPRITE_H
#define CUSTOMSPRITE_H

#include <Arduino.h>

class CustomSprite {
public:
    CustomSprite(int8_t x, int8_t y);
    
    // Sprite properties
    uint8_t _totalFrames = 0;
    uint8_t _currentFrame = 0;
    uint8_t _spriteReference = 0;
    uint8_t _currentFrameCount = 0;
    unsigned long _lastMillisSpriteFrames = 0;
    unsigned long _lastResetTime = 0;
    unsigned long _lastResetMoveTime = 0;
    
    // Movement properties
    bool _moving = false;
    unsigned long _moveStartTime = 1;
    unsigned long _moveDuration = 0;
    int8_t _moveInitialX = 0;
    int8_t _moveInitialY = 0;
    int8_t _moveTargetX = -1;
    int8_t _moveTargetY = -1;
    bool _shouldReturnToOrigin = false;
    bool _isReversing = false;
    
    // Position and dimensions
    void setDimensions(uint8_t width, uint8_t height);
    int8_t getX();
    int8_t getY();
    uint8_t getWidth();
    uint8_t getHeight();
    void setX(int8_t newX);
    void setY(int8_t newY);
    
    // Animation
    void incFrame();
    
    // Movement methods
    void startMoving(int8_t targetX, int8_t targetY, unsigned long duration, bool shouldReturnToOrigin);
    void reverseMoving(int8_t targetX, int8_t targetY);
    void stopMoving();
    bool isMoving();
    bool shouldReturnToOrigin();
    
    // Linear interpolation
    int8_t lerp(int8_t start, int8_t end, float t);

private:
    int8_t _x, _y;
    uint8_t _width, _height;
};

#endif