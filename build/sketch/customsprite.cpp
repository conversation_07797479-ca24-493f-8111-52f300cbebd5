#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/customsprite.cpp"
#include "customsprite.h"

CustomSprite::CustomSprite(int8_t x, int8_t y) {
    _x = x;
    _y = y;
    _width = 16; // Default size
    _height = 16;
}

void CustomSprite::setDimensions(uint8_t width, uint8_t height) {
    _width = width;
    _height = height;
}

int8_t CustomSprite::getX() {
    return _x;
}

int8_t CustomSprite::getY() {
    return _y;
}

uint8_t CustomSprite::getWidth() {
    return _width;
}

uint8_t CustomSprite::getHeight() {
    return _height;
}

void CustomSprite::setX(int8_t newX) {
    _x = newX;
}

void CustomSprite::setY(int8_t newY) {
    _y = newY;
}

void CustomSprite::incFrame() {
    _currentFrame++;
    if (_currentFrame >= _totalFrames) {
        _currentFrame = 0;
    }
}

void CustomSprite::startMoving(int8_t targetX, int8_t targetY, unsigned long duration, bool shouldReturnToOrigin) {
    _moveStartTime = millis();
    _moveDuration = duration;
    _moveInitialX = getX();
    _moveInitialY = getY();
    _moveTargetX = targetX;
    _moveTargetY = targetY;
    _shouldReturnToOrigin = shouldReturnToOrigin;
    _moving = true;
    _isReversing = false;
}

void CustomSprite::reverseMoving(int8_t targetX, int8_t targetY) {
    _moveStartTime = millis();
    _moveInitialX = getX();
    _moveInitialY = getY();
    _moveTargetX = targetX;
    _moveTargetY = targetY;
    _shouldReturnToOrigin = false;
    _moving = true;
    _isReversing = true;
}

void CustomSprite::stopMoving() {
    _moving = false;
}

bool CustomSprite::isMoving() {
    return _moving;
}

bool CustomSprite::shouldReturnToOrigin() {
    return _shouldReturnToOrigin;
}

int8_t CustomSprite::lerp(int8_t start, int8_t end, float t) {
    if (t > 1.0f) t = 1.0f;
    if (t < 0.0f) t = 0.0f;
    return start + static_cast<int8_t>(t * (end - start));
}