#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone_clockface/gif.h"
#ifndef GIF_H
#define GIF_H

#include <AnimatedGIF.h>
#include <SPIFFS.h>
#include "display.h"

// GIF related globals
extern AnimatedGIF gif;
extern File f;
extern int x_offset, y_offset;
extern String currentGifPath;
extern String requestedGifPath;
extern String gifDir;
extern char filePath[256];
extern File root, gifFile;

// GIF functions
void GIFDraw(GIFDRAW *pDraw);
void *GIFOpenFile(const char *fname, int32_t *pSize);
void GIFCloseFile(void *pHandle);
int32_t GIFReadFile(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen);
int32_t GIFSeekFile(GIFFILE *pFile, int32_t iPosition);
void ShowGIF(char *name);
void ShowClockface(const char *filename);
void UpdateDisplayLayers();
void ForceDisplayScrollText();
bool isGifFile(const String& fileName);
bool isMediaFile(const String& fileName);
bool isClockfaceFile(const String& fileName);

#endif